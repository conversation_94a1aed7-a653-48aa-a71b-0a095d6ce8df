'use client'

import { XMarkIcon } from '@heroicons/react/24/outline';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';

const applicationSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().optional(),
  resume: z
    .instanceof(FileList)
    .refine((file) => file && file.length === 1, { message: 'Resume (PDF) is required' })
    .refine((file) => file && file.length === 1 && file[0].type === 'application/pdf', { message: 'Only PDF files are allowed' }),
  coverletter: z.string().min(1, 'Cover letter is required'),
});

type ApplicationFormData = z.infer<typeof applicationSchema>;

interface JobApplicationModalProps {
  isOpen: boolean;
  onClose: () => void;
  jobId: string | number;
  onSuccess?: () => void;
}

export default function JobApplicationModal({ isOpen, onClose, jobId, onSuccess }: JobApplicationModalProps) {
  const [loading, setLoading] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const {
    register,
    handleSubmit,
    reset,
    control,
    formState: { errors },
  } = useForm<ApplicationFormData>({
    resolver: zodResolver(applicationSchema),
  });

  const onSubmit = async (data: ApplicationFormData) => {
    setLoading(true);
    setSubmitError(null);
    setSuccess(false);
    try {
      const formData = new FormData();
      formData.append('name', data.name);
      formData.append('email', data.email);
      formData.append('phone', data.phone || '');
      formData.append('coverletter', data.coverletter);
      formData.append('resume', data.resume[0]);
      formData.append('jobId', String(jobId));
      const res = await fetch('/api/careers/apply', {
        method: 'POST',
        body: formData,
      });
      if (!res.ok) throw new Error('Failed to submit application');
      setSuccess(true);
      reset();
      if (fileInputRef.current) fileInputRef.current.value = '';
      if (onSuccess) onSuccess();
    } catch (err: any) {
      setSubmitError(err.message || 'Failed to submit application');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="fixed inset-0 bg-gray-500 bg-opacity-75" />
      <div className="flex items-center justify-center min-h-screen px-4 py-8">
        <div className="relative bg-white rounded-lg shadow-xl max-w-lg w-full mx-auto p-6" onClick={e => e.stopPropagation()}>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-bold text-gray-900">Apply for this Job</h3>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-500"><XMarkIcon className="h-6 w-6" /></button>
          </div>
          {success ? (
            <div className="p-4 bg-green-50 border border-green-200 rounded mb-4 text-green-700">Application submitted successfully!</div>
          ) : (
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Name *</label>
                <input type="text" {...register('name')} className="block w-full px-3 py-2 border rounded focus:outline-none focus:ring focus:border-blue-500" />
                {typeof errors.name?.message === 'string' && <p className="text-red-600 text-sm mt-1">{errors.name.message}</p>}
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Email *</label>
                <input type="email" {...register('email')} className="block w-full px-3 py-2 border rounded focus:outline-none focus:ring focus:border-blue-500" />
                {typeof errors.email?.message === 'string' && <p className="text-red-600 text-sm mt-1">{errors.email.message}</p>}
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Phone</label>
                <input type="tel" {...register('phone')} className="block w-full px-3 py-2 border rounded focus:outline-none focus:ring focus:border-blue-500" />
                {typeof errors.phone?.message === 'string' && <p className="text-red-600 text-sm mt-1">{errors.phone.message}</p>}
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Resume (PDF) *</label>
                <Controller
                  name="resume"
                  control={control}
                  defaultValue={undefined}
                  render={({ field }) => (
                    <input
                      type="file"
                      accept="application/pdf"
                      onChange={e => field.onChange(e.target.files)}
                      ref={fileInputRef}
                      className="block w-full text-sm"
                    />
                  )}
                />
                {typeof errors.resume?.message === 'string' && <p className="text-red-600 text-sm mt-1">{errors.resume.message}</p>}
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Cover Letter *</label>
                <textarea {...register('coverletter')} rows={4} className="block w-full px-3 py-2 border rounded focus:outline-none focus:ring focus:border-blue-500" />
                {typeof errors.coverletter?.message === 'string' && <p className="text-red-600 text-sm mt-1">{errors.coverletter.message}</p>}
              </div>
              {submitError && <div className="text-red-600 text-sm mb-2">{submitError}</div>}
              <button type="submit" className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 transition font-semibold" disabled={loading}>
                {loading ? 'Submitting...' : 'Submit Application'}
              </button>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}
