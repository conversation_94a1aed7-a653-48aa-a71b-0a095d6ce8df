'use client'

import { XMarkIcon } from '@heroicons/react/24/outline';
import { useEffect, useState } from 'react';

interface Job {
  id: string | number;
  title: string;
}

interface JobApplicationsModalProps {
  isOpen: boolean;
  onClose: () => void;
  job: Job | null;
}

interface Application {
  id: string | number;
  applicantname: string;
  applicantemail: string;
  status: string;
  createdat: string;
  resumeurl?: string;
  coverletter?: string;
}

export default function JobApplicationsModal({ isOpen, onClose, job }: JobApplicationsModalProps) {
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && job?.id) {
      setLoading(true);
      setError(null);
      fetch(`/api/admin/job-applications?jobListingId=${job.id}`)
        .then(res => res.json())
        .then(data => {
          if (data.success === false) {
            setError(data.error || 'Failed to load applications');
          } else {
            setApplications(data.data || []);
          }
        })
        .catch(() => setError('Failed to load applications'))
        .finally(() => setLoading(false));
    } else {
      setApplications([]);
    }
  }, [isOpen, job]);

  if (!isOpen || !job) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4">
        <div className="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm" onClick={onClose} />
        <div className="relative bg-white rounded-xl shadow-2xl max-w-3xl w-full max-h-[90vh] overflow-y-auto p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold">Applications for: {job.title}</h2>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600"><XMarkIcon className="h-6 w-6" /></button>
          </div>
          {loading ? (
            <div className="py-8 text-center text-gray-500">Loading...</div>
          ) : error ? (
            <div className="py-8 text-center text-red-500">{error}</div>
          ) : applications.length === 0 ? (
            <div className="py-8 text-center text-gray-500">No applications found for this job.</div>
          ) : (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Name</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Email</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Resume</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {applications.map(app => (
                  <tr key={app.id}>
                    <td className="px-4 py-2 text-sm">{app.applicantname}</td>
                    <td className="px-4 py-2 text-sm">{app.applicantemail}</td>
                    <td className="px-4 py-2 text-sm">{app.status}</td>
                    <td className="px-4 py-2 text-sm">{new Date(app.createdat).toLocaleDateString()}</td>
                    <td className="px-4 py-2 text-sm">
                      {app.resumeurl ? (
                        <a href={app.resumeurl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">View</a>
                      ) : (
                        <span className="text-gray-400">N/A</span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </div>
  );
}
