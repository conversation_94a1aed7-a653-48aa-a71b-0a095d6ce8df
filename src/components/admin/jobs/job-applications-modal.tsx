'use client'

import {
    BriefcaseIcon,
    CalendarIcon,
    CheckCircleIcon,
    ChevronRightIcon,
    ClockIcon,
    DocumentArrowDownIcon,
    DocumentTextIcon,
    EnvelopeIcon,
    EyeIcon,
    LinkIcon,
    PhoneIcon,
    UserIcon,
    XCircleIcon,
    XMarkIcon
} from '@heroicons/react/24/outline';
import { useEffect, useState } from 'react';

interface Job {
  id: string | number;
  title: string;
}

interface JobApplicationsModalProps {
  isOpen: boolean;
  onClose: () => void;
  job: Job | null;
}

interface Application {
  id: string | number;
  applicantname: string;
  applicantemail: string;
  applicantphone?: string;
  status: string;
  createdat: string;
  updatedat?: string;
  resumeurl?: string;
  coverletter?: string;
  notes?: string;
}

export default function JobApplicationsModal({ isOpen, onClose, job }: JobApplicationsModalProps) {
  const [applications, setApplications] = useState<Application[]>([]);
  const [selectedApplication, setSelectedApplication] = useState<Application | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [statusUpdating, setStatusUpdating] = useState(false);

  useEffect(() => {
    if (isOpen && job?.id) {
      setLoading(true);
      setError(null);
      setSelectedApplication(null);
      fetch(`/api/admin/job-applications?jobListingId=${job.id}`)
        .then(res => res.json())
        .then(data => {
          if (data.success === false) {
            setError(data.error || 'Failed to load applications');
          } else {
            const apps = data.data || [];
            setApplications(apps);
            // Auto-select first application if available
            if (apps.length > 0) {
              setSelectedApplication(apps[0]);
            }
          }
        })
        .catch(() => setError('Failed to load applications'))
        .finally(() => setLoading(false));
    } else {
      setApplications([]);
      setSelectedApplication(null);
    }
  }, [isOpen, job]);

  const updateApplicationStatus = async (applicationId: string | number, newStatus: string) => {
    setStatusUpdating(true);
    try {
      const response = await fetch(`/api/admin/job-applications/${applicationId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        // Update local state
        setApplications(prev =>
          prev.map(app =>
            app.id === applicationId ? { ...app, status: newStatus } : app
          )
        );
        if (selectedApplication?.id === applicationId) {
          setSelectedApplication(prev => prev ? { ...prev, status: newStatus } : null);
        }
      }
    } catch (error) {
      console.error('Failed to update status:', error);
    } finally {
      setStatusUpdating(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'reviewing': return 'bg-blue-100 text-blue-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'interviewed': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending': return ClockIcon;
      case 'reviewing': return EyeIcon;
      case 'approved': return CheckCircleIcon;
      case 'rejected': return XCircleIcon;
      case 'interviewed': return UserIcon;
      default: return ClockIcon;
    }
  };

  const parseAdditionalInfo = (notes: string) => {
    try {
      return JSON.parse(notes);
    } catch {
      return null;
    }
  };

  if (!isOpen || !job) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm" onClick={onClose} />
      <div className="flex items-center justify-center min-h-screen p-4">
        <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-7xl h-[90vh] flex flex-col">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-t-2xl p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold">Job Applications</h2>
                <p className="text-blue-100 mt-1">{job.title} • {applications.length} applications</p>
              </div>
              <button
                onClick={onClose}
                className="text-white hover:text-gray-200 transition-colors p-2 rounded-full hover:bg-white/10"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 flex overflow-hidden">
            {loading ? (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-500">Loading applications...</p>
                </div>
              </div>
            ) : error ? (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center">
                  <XCircleIcon className="w-16 h-16 text-red-500 mx-auto mb-4" />
                  <p className="text-red-500 text-lg font-medium">{error}</p>
                </div>
              </div>
            ) : applications.length === 0 ? (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center">
                  <UserIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Applications Yet</h3>
                  <p className="text-gray-500">No applications have been submitted for this position.</p>
                </div>
              </div>
            ) : (
              <>
                {/* Section 1: Applicants List */}
                <div className="w-1/3 border-r border-gray-200 bg-gray-50">
                  <div className="p-4 border-b border-gray-200 bg-white">
                    <h3 className="text-lg font-semibold text-gray-900">Applicants ({applications.length})</h3>
                  </div>
                  <div className="overflow-y-auto h-full">
                    {applications.map((app) => {
                      const StatusIcon = getStatusIcon(app.status);
                      return (
                        <div
                          key={app.id}
                          onClick={() => setSelectedApplication(app)}
                          className={`p-4 border-b border-gray-200 cursor-pointer transition-colors hover:bg-white ${
                            selectedApplication?.id === app.id ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center">
                                <div className="flex-shrink-0">
                                  <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                                    <UserIcon className="w-5 h-5 text-gray-500" />
                                  </div>
                                </div>
                                <div className="ml-3 flex-1 min-w-0">
                                  <p className="text-sm font-medium text-gray-900 truncate">
                                    {app.applicantname}
                                  </p>
                                  <p className="text-sm text-gray-500 truncate">
                                    {app.applicantemail}
                                  </p>
                                </div>
                              </div>
                              <div className="mt-2 flex items-center justify-between">
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(app.status)}`}>
                                  <StatusIcon className="w-3 h-3 mr-1" />
                                  {app.status}
                                </span>
                                <span className="text-xs text-gray-400">
                                  {new Date(app.createdat).toLocaleDateString()}
                                </span>
                              </div>
                            </div>
                            <ChevronRightIcon className="w-4 h-4 text-gray-400 ml-2" />
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Section 2: Application Details */}
                <div className="flex-1 flex flex-col">
                  {selectedApplication ? (
                    <>
                      <div className="p-6 border-b border-gray-200 bg-white">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="text-xl font-semibold text-gray-900">{selectedApplication.applicantname}</h3>
                            <p className="text-gray-600">{selectedApplication.applicantemail}</p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <select
                              value={selectedApplication.status}
                              onChange={(e) => updateApplicationStatus(selectedApplication.id, e.target.value)}
                              disabled={statusUpdating}
                              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            >
                              <option value="PENDING">Pending</option>
                              <option value="REVIEWING">Reviewing</option>
                              <option value="INTERVIEWED">Interviewed</option>
                              <option value="APPROVED">Approved</option>
                              <option value="REJECTED">Rejected</option>
                            </select>
                          </div>
                        </div>
                      </div>

                      <div className="flex-1 overflow-y-auto p-6">
                        <div className="space-y-6">
                          {/* Contact Information */}
                          <div>
                            <h4 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                              <EnvelopeIcon className="w-5 h-5 mr-2 text-blue-600" />
                              Contact Information
                            </h4>
                            <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                              <div className="flex items-center">
                                <EnvelopeIcon className="w-4 h-4 text-gray-400 mr-2" />
                                <span className="text-sm">{selectedApplication.applicantemail}</span>
                              </div>
                              {selectedApplication.applicantphone && (
                                <div className="flex items-center">
                                  <PhoneIcon className="w-4 h-4 text-gray-400 mr-2" />
                                  <span className="text-sm">{selectedApplication.applicantphone}</span>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Additional Information */}
                          {selectedApplication.notes && (() => {
                            const additionalInfo = parseAdditionalInfo(selectedApplication.notes);
                            return additionalInfo ? (
                              <div>
                                <h4 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                                  <BriefcaseIcon className="w-5 h-5 mr-2 text-blue-600" />
                                  Professional Information
                                </h4>
                                <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                                  {additionalInfo.currentPosition && (
                                    <div>
                                      <span className="text-sm font-medium text-gray-700">Current Position:</span>
                                      <span className="text-sm text-gray-900 ml-2">{additionalInfo.currentPosition}</span>
                                    </div>
                                  )}
                                  {additionalInfo.currentCompany && (
                                    <div>
                                      <span className="text-sm font-medium text-gray-700">Current Company:</span>
                                      <span className="text-sm text-gray-900 ml-2">{additionalInfo.currentCompany}</span>
                                    </div>
                                  )}
                                  {additionalInfo.yearsOfExperience && (
                                    <div>
                                      <span className="text-sm font-medium text-gray-700">Experience:</span>
                                      <span className="text-sm text-gray-900 ml-2">{additionalInfo.yearsOfExperience} years</span>
                                    </div>
                                  )}
                                  {additionalInfo.location && (
                                    <div>
                                      <span className="text-sm font-medium text-gray-700">Location:</span>
                                      <span className="text-sm text-gray-900 ml-2">{additionalInfo.location}, {additionalInfo.country}</span>
                                    </div>
                                  )}
                                  {additionalInfo.expectedSalary && (
                                    <div>
                                      <span className="text-sm font-medium text-gray-700">Expected Salary:</span>
                                      <span className="text-sm text-gray-900 ml-2">{additionalInfo.expectedSalary}</span>
                                    </div>
                                  )}
                                  {additionalInfo.availabilityDate && (
                                    <div>
                                      <span className="text-sm font-medium text-gray-700">Available From:</span>
                                      <span className="text-sm text-gray-900 ml-2">{new Date(additionalInfo.availabilityDate).toLocaleDateString()}</span>
                                    </div>
                                  )}
                                  {additionalInfo.workAuthorization && (
                                    <div>
                                      <span className="text-sm font-medium text-gray-700">Work Authorization:</span>
                                      <span className="text-sm text-gray-900 ml-2">{additionalInfo.workAuthorization.replace('_', ' ')}</span>
                                    </div>
                                  )}
                                  {additionalInfo.remotePreference && (
                                    <div>
                                      <span className="text-sm font-medium text-gray-700">Remote Preference:</span>
                                      <span className="text-sm text-gray-900 ml-2">{additionalInfo.remotePreference.replace('_', ' ')}</span>
                                    </div>
                                  )}

                                  {/* Portfolio Links */}
                                  {(additionalInfo.portfolioUrl || additionalInfo.linkedinUrl || additionalInfo.githubUrl) && (
                                    <div className="pt-2 border-t border-gray-200">
                                      <span className="text-sm font-medium text-gray-700 block mb-2">Portfolio & Links:</span>
                                      <div className="space-y-1">
                                        {additionalInfo.portfolioUrl && (
                                          <a href={additionalInfo.portfolioUrl} target="_blank" rel="noopener noreferrer" className="flex items-center text-sm text-blue-600 hover:underline">
                                            <LinkIcon className="w-4 h-4 mr-1" />
                                            Portfolio
                                          </a>
                                        )}
                                        {additionalInfo.linkedinUrl && (
                                          <a href={additionalInfo.linkedinUrl} target="_blank" rel="noopener noreferrer" className="flex items-center text-sm text-blue-600 hover:underline">
                                            <LinkIcon className="w-4 h-4 mr-1" />
                                            LinkedIn
                                          </a>
                                        )}
                                        {additionalInfo.githubUrl && (
                                          <a href={additionalInfo.githubUrl} target="_blank" rel="noopener noreferrer" className="flex items-center text-sm text-blue-600 hover:underline">
                                            <LinkIcon className="w-4 h-4 mr-1" />
                                            GitHub
                                          </a>
                                        )}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            ) : null;
                          })()}

                          {/* Cover Letter */}
                          {selectedApplication.coverletter && (
                            <div>
                              <h4 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                                <DocumentTextIcon className="w-5 h-5 mr-2 text-blue-600" />
                                Cover Letter
                              </h4>
                              <div className="bg-gray-50 rounded-lg p-4">
                                <p className="text-sm text-gray-700 whitespace-pre-line leading-relaxed">
                                  {selectedApplication.coverletter}
                                </p>
                              </div>
                            </div>
                          )}

                          {/* Application Timeline */}
                          <div>
                            <h4 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                              <CalendarIcon className="w-5 h-5 mr-2 text-blue-600" />
                              Timeline
                            </h4>
                            <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                              <div className="flex items-center text-sm">
                                <ClockIcon className="w-4 h-4 text-gray-400 mr-2" />
                                <span className="text-gray-700">Applied on {new Date(selectedApplication.createdat).toLocaleDateString()}</span>
                              </div>
                              {selectedApplication.updatedat && selectedApplication.updatedat !== selectedApplication.createdat && (
                                <div className="flex items-center text-sm">
                                  <ClockIcon className="w-4 h-4 text-gray-400 mr-2" />
                                  <span className="text-gray-700">Last updated on {new Date(selectedApplication.updatedat).toLocaleDateString()}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="flex-1 flex items-center justify-center">
                      <div className="text-center">
                        <UserIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Select an Applicant</h3>
                        <p className="text-gray-500">Choose an applicant from the list to view their details.</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Section 3: Resume Preview */}
                <div className="w-1/3 border-l border-gray-200 bg-gray-50">
                  <div className="p-4 border-b border-gray-200 bg-white">
                    <h3 className="text-lg font-semibold text-gray-900">Resume Preview</h3>
                  </div>
                  <div className="h-full flex flex-col">
                    {selectedApplication?.resumeurl ? (
                      <>
                        <div className="p-4 bg-white border-b border-gray-200">
                          <a
                            href={selectedApplication.resumeurl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                          >
                            <DocumentArrowDownIcon className="w-4 h-4 mr-2" />
                            Download Resume
                          </a>
                        </div>
                        <div className="flex-1 p-4">
                          <iframe
                            src={selectedApplication.resumeurl}
                            className="w-full h-full border border-gray-300 rounded-lg"
                            title="Resume Preview"
                          />
                        </div>
                      </>
                    ) : (
                      <div className="flex-1 flex items-center justify-center">
                        <div className="text-center">
                          <DocumentTextIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-lg font-medium text-gray-900 mb-2">No Resume Available</h3>
                          <p className="text-gray-500">
                            {selectedApplication ? 'This applicant has not uploaded a resume.' : 'Select an applicant to view their resume.'}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
