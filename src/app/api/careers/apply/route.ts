import { prisma } from '@/config/prisma';
import { writeFile } from 'fs/promises';
import { NextResponse } from 'next/server';
import path from 'path';

export const runtime = 'nodejs';

export async function POST(req: Request) {
  try {
    const formData = await req.formData();
    const name = formData.get('name') as string;
    const email = formData.get('email') as string;
    const phone = formData.get('phone') as string;
    const coverletter = formData.get('coverletter') as string;
    const jobId = Number(formData.get('jobId'));
    const resume = formData.get('resume') as File;

    if (!name || !email || !coverletter || !resume || !jobId) {
      return NextResponse.json({ error: 'Missing required fields.' }, { status: 400 });
    }
    if (resume.type !== 'application/pdf') {
      return NextResponse.json({ error: 'Resume must be a PDF.' }, { status: 400 });
    }

    // Save resume file
    const buffer = Buffer.from(await resume.arrayBuffer());
    const fileName = `resume-${Date.now()}.pdf`;
    const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'resumes');
    const filePath = path.join(uploadDir, fileName);
    await writeFile(filePath, buffer);
    const resumeUrl = `/uploads/resumes/${fileName}`;

    // Store application in DB
    await prisma.jobapplications.create({
      data: {
        applicantname: name,
        applicantemail: email,
        applicantphone: phone,
        resumeurl: resumeUrl,
        coverletter,
        joblistingid: BigInt(jobId),
        status: 'PENDING',
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Failed to submit application.' }, { status: 500 });
  }
}
