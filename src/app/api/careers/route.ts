import { prisma } from '@/config/prisma';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    console.log('Fetching job listings...');
    const jobs = await prisma.joblistings.findMany({
      where: { isactive: true },
      orderBy: { createdat: 'desc' },
      select: {
        id: true,
        title: true,
        location: true,
        employmenttype: true,
        description: true,
        createdat: true,
        updatedat: true,
      },
    });
    console.log(`Found ${jobs.length} jobs`);

    // Convert BigInt to string for JSON serialization
    const serializedJobs = jobs.map(job => ({
      ...job,
      id: job.id.toString(),
    }));

    return NextResponse.json(serializedJobs);
  } catch (error) {
    console.error('Error fetching job listings:', error);
    return NextResponse.json({ error: 'Failed to fetch job listings.' }, { status: 500 });
  }
}
