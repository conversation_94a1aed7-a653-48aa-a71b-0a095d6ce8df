import { prisma } from '@/config/prisma';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    const jobs = await prisma.joblistings.findMany({
      where: { isactive: true },
      orderBy: { createdat: 'desc' },
      select: {
        id: true,
        title: true,
        location: true,
        employmenttype: true,
        description: true,
        createdat: true,
        updatedat: true,
      },
    });
    return NextResponse.json(jobs);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch job listings.' }, { status: 500 });
  }
}
