import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import { 
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/services/api/api-utils'
import { schemas } from '@/lib/utils/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/job-applications/[id] - Get a specific job application
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const jobApplication = await prisma.jobapplications.findUnique({
    where: { id },
    include: {
      jobListing: {
        select: {
          id: true,
          title: true,
          description: true,
          location: true,
          employmenttype: true,
          salaryRange: true,
          requirements: true,
          isactive: true
        }
      }
    }
  })

  if (!jobApplication) {
    throw new ApiError('Job application not found', 404)
  }

  return successResponse(jobApplication)
})

// PUT /api/admin/job-applications/[id] - Update a job application
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const validate = validateRequest(schemas.jobApplication.update)
  const data = await validate(request)

  // Check if job application exists
  const existingApplication = await prisma.jobapplications.findUnique({
    where: { id },
  })

  if (!existingApplication) {
    throw new ApiError('Job application not found', 404)
  }

  const jobApplication = await prisma.jobapplications.update({
    where: { id },
    data,
    include: {
      jobListing: {
        select: {
          id: true,
          title: true,
          location: true,
          employmenttype: true
        }
      }
    }
  })

  return successResponse(jobApplication, 'Job application updated successfully')
})

// DELETE /api/admin/job-applications/[id] - Delete a job application
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  // Check if job application exists
  const existingApplication = await prisma.jobapplications.findUnique({
    where: { id },
  })

  if (!existingApplication) {
    throw new ApiError('Job application not found', 404)
  }

  await prisma.jobapplications.delete({
    where: { id }
  })

  return successResponse(null, 'Job application deleted successfully')
})

// PATCH /api/admin/job-applications/[id] - Update application status
export const PATCH = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const body = await request.json()

  // Check if job application exists
  const existingApplication = await prisma.jobapplications.findUnique({
    where: { id },
  })

  if (!existingApplication) {
    throw new ApiError('Job application not found', 404)
  }

  // Only allow status updates for PATCH
  const allowedFields = ['status', 'notes']
  const updateData: any = {}

  for (const field of allowedFields) {
    if (body[field] !== undefined) {
      updateData[field] = body[field]
    }
  }

  if (Object.keys(updateData).length === 0) {
    throw new ApiError('No valid fields to update', 400)
  }

  const jobApplication = await prisma.jobapplications.update({
    where: { id },
    data: updateData,
    include: {
      jobListing: {
        select: {
          id: true,
          title: true,
          location: true,
          employmenttype: true
        }
      }
    }
  })

  return successResponse(jobApplication, 'Job application updated successfully')
})
