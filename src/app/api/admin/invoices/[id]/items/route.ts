import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import { 
  withError<PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/services/api/api-utils'
import { z } from 'zod'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

const createInvoiceItemSchema = z.object({
  description: z.string().min(1),
  quantity: z.coerce.number().int().positive().default(1),
  unitPrice: z.coerce.number().positive(),
  totalPrice: z.coerce.number().positive(),
})

const updateInvoiceItemSchema = createInvoiceItemSchema.partial()

// GET /api/admin/invoices/[id]/items - Get all items for an invoice
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const items = await prisma.invoicesItem.findMany({
    where: { invoiceId: id },
    orderBy: { createdat: 'asc' }
  })

  return successResponse(items)
})

// POST /api/admin/invoices/[id]/items - Add item to invoice
export const POST = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const validate = validateRequest(createInvoiceItemSchema)
  const data = await validate(request)

  // Check if invoice exists
  const invoice = await prisma.invoices.findUnique({
    where: { id }
  })

  if (!invoice) {
    throw new ApiError('Invoice not found', 404)
  }

  const item = await prisma.invoicesItem.create({
    data: {
      ...data,
      invoiceId: id
    }
  })

  // Recalculate invoice totals
  const items = await prisma.invoicesItem.findMany({
    where: { invoiceId: id }
  })

  const subtotal = items.reduce((sum, item) => sum + Number(item.totalPrice), 0)
  const totalAmount = subtotal + Number(invoice.taxamount)

  await prisma.invoices.update({
    where: { id },
    data: {
      subtotal,
      totalAmount
    }
  })

  return successResponse(item, 'Invoice item added successfully', 201)
})
