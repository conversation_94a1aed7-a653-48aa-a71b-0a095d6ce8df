import { headers } from 'next/headers';
import Link from 'next/link';
import { Suspense } from 'react';

async function fetchJobs() {
  let url = '';
  if (typeof window === 'undefined') {
    // Server-side: build absolute URL
    const headersList = await headers();
    const host = headersList.get('host');
    const protocol = headersList.get('x-forwarded-proto') || 'http';
    url = `${protocol}://${host}/api/careers`;
  } else {
    // Client-side
    url = '/api/careers';
  }
  const res = await fetch(url, { cache: 'no-store' });
  if (!res.ok) throw new Error('Failed to fetch jobs');
  return res.json();
}

function JobCard({ job }: { job: any }) {
  return (
    <div className="bg-white rounded-lg shadow p-6 flex flex-col justify-between h-full border border-gray-100">
      <div>
        <h2 className="text-xl font-semibold mb-2">{job.title}</h2>
        <div className="text-gray-500 text-sm mb-1">{job.location}</div>
        <div className="text-gray-500 text-sm mb-1">{job.employmenttype}</div>
        <p className="text-gray-700 mb-4 line-clamp-3">{job.description}</p>
      </div>
      <Link href={`/careers/${job.id}`} className="mt-auto inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition">View Details</Link>
    </div>
  );
}

async function JobsList() {
  let jobs: any[] = [];
  try {
    jobs = await fetchJobs();
  } catch {
    return <div className="text-red-500">Failed to load jobs.</div>;
  }
  if (!jobs.length) {
    return <div className="text-gray-500 text-center py-12">No open positions at this time. Please check back later.</div>;
  }
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
      {jobs.map(job => <JobCard key={job.id} job={job} />)}
    </div>
  );
}

export default function CareersPage() {
  return (
    <main className="max-w-5xl mx-auto px-4 py-12">
      <h1 className="text-3xl font-bold mb-2">Careers</h1>
      <p className="text-gray-600 mb-8">Join our team and help shape the future. Explore our open positions below.</p>
      <Suspense fallback={<div className="text-center py-12">Loading jobs...</div>}>
        <JobsList />
      </Suspense>
    </main>
  );
}
