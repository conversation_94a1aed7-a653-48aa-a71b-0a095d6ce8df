'use client';

import dynamic from 'next/dynamic';
import { useState } from 'react';

const JobApplicationModal = dynamic(() => import('@/components/client/job-application-modal'), { ssr: false });

interface Job {
  id: string;
  title: string;
  location: string;
  employmenttype: string;
  description: string;
  requirements: string;
  salarymin?: string;
  salarymax?: string;
  salarycurrency?: string;
  isremote?: boolean;
}

interface JobDetailsClientProps {
  job: Job;
}

export default function JobDetailsClient({ job }: JobDetailsClientProps) {
  const [showModal, setShowModal] = useState(false);

  return (
    <main className="max-w-2xl mx-auto px-4 py-12">
      <h1 className="text-2xl font-bold mb-2">{job.title}</h1>
      <div className="flex flex-wrap gap-4 text-gray-600 mb-4">
        <span>{job.location}</span>
        <span>|</span>
        <span>{job.employmenttype}</span>
        {job.isremote && <span className="bg-blue-100 text-blue-700 px-2 py-0.5 rounded text-xs">Remote</span>}
      </div>
      <div className="mb-6">
        <div className="text-gray-800 font-semibold mb-1">Description</div>
        <div className="text-gray-700 whitespace-pre-line mb-4">{job.description}</div>
        <div className="text-gray-800 font-semibold mb-1">Requirements</div>
        <div className="text-gray-700 whitespace-pre-line mb-4">{job.requirements}</div>
        {(job.salarymin || job.salarymax) && (
          <div className="text-gray-800 font-semibold mb-1">Salary</div>
        )}
        {(job.salarymin || job.salarymax) && (
          <div className="text-gray-700 mb-4">
            {job.salarymin && job.salarymax
              ? `$${job.salarymin} - $${job.salarymax} ${job.salarycurrency || ''}`
              : job.salarymin
              ? `$${job.salarymin} ${job.salarycurrency || ''}`
              : job.salarymax
              ? `$${job.salarymax} ${job.salarycurrency || ''}`
              : ''}
          </div>
        )}
      </div>
      <button 
        className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 transition font-semibold" 
        type="button" 
        onClick={() => setShowModal(true)}
      >
        Apply Now
      </button>
      <JobApplicationModal 
        isOpen={showModal} 
        onClose={() => setShowModal(false)} 
        jobId={job.id} 
      />
    </main>
  );
}
